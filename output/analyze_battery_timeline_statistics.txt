{"total_batteries": 18017, "batteries_with_type": 18017, "batteries_without_type": 0, "total_intervals": 29225, "intervals_with_km_stand": 28117, "errors": ["Negative km_stand for VIN WS5D16GAAJA800547: start_km=21.4, end_km=6.3. Start date: 2018-07-19, End date: 2025-01-10", "Negative km_stand for VIN WS5D16GAAJB700004: start_km=2440.7, end_km=10.5. Start date: 2020-10-26, End date: 2021-02-12", "Negative km_stand for VIN WS5D16GAAJA101756: start_km=43625.7, end_km=36073.3. Start date: 2022-01-31, End date: 2025-07-28", "Negative km_stand for VIN WS5B16BBBHA700165: start_km=307.8, end_km=298.7. Start date: 2020-10-06, End date: 2021-06-11", "Negative km_stand for VIN WS5B16BABGA101648: start_km=24620.3, end_km=7788.4. Start date: 2022-09-08, End date: 2025-07-28", "Negative km_stand for VIN WF0JXXTTGJHE64066: start_km=34361.0, end_km=3947.0. Start date: 2024-07-09, End date: 2025-07-28"], "processing_date": "2025-07-29 12:48:01.690500", "battery_age_output_file": "output/accure.csv", "detailed_lifecycle_output_file": "output/battery_lifecycle_detailed.csv", "total_battery_age_records": 18017, "total_detailed_records": 29225}